import React, { Suspense, useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { 
  OrbitControls, 
  PerspectiveCamera, 
  Environment, 
  ContactShadows,
  useGLTF,
  Html,
  Stats
} from '@react-three/drei';
import { 
  RotateCcw, 
  ZoomIn, 
  ZoomOut, 
  Sun,
  Eye,
  Grid3X3,
  Info
} from 'lucide-react';
import { DigitalTwinModel } from '../App';

interface ModelViewerProps {
  model: DigitalTwinModel | null;
}

const Model: React.FC<{ url: string }> = ({ url }) => {
  const { scene } = useGLTF(url);
  const meshRef = useRef<any>();

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.1;
    }
  });

  return <primitive ref={meshRef} object={scene} scale={[1, 1, 1]} />;
};

const LoadingScreen: React.FC = () => (
  <Html center>
    <div className="flex flex-col items-center space-y-4 p-8 bg-slate-800/90 backdrop-blur-sm rounded-xl border border-slate-700/50">
      <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
      <div className="text-white font-medium">Loading 3D Model...</div>
      <div className="text-slate-400 text-sm">Processing geometry and materials</div>
    </div>
  </Html>
);

export const ModelViewer: React.FC<ModelViewerProps> = ({ model }) => {
  const [wireframe, setWireframe] = useState(false);
  const [showGrid, setShowGrid] = useState(true);
  const [lighting, setLighting] = useState<'studio' | 'sunset' | 'city'>('studio');
  const cameraRef = useRef<any>();

  const resetCamera = () => {
    if (cameraRef.current) {
      cameraRef.current.position.set(5, 5, 5);
      cameraRef.current.lookAt(0, 0, 0);
    }
  };

  return (
    <div className="h-full relative">
      {/* Controls Bar */}
      <div className="absolute top-6 left-6 right-6 z-10 flex justify-between items-center">
        <div className="flex items-center space-x-2 bg-slate-800/80 backdrop-blur-sm rounded-xl p-2 border border-slate-700/50">
          <button
            onClick={resetCamera}
            className="p-2 hover:bg-slate-700/50 rounded-lg transition-colors text-slate-300 hover:text-white"
            title="Reset Camera"
          >
            <RotateCcw className="w-5 h-5" />
          </button>
          <div className="w-px h-6 bg-slate-600" />
          <button
            onClick={() => setWireframe(!wireframe)}
            className={`p-2 rounded-lg transition-colors ${
              wireframe 
                ? 'bg-blue-500/20 text-blue-400' 
                : 'hover:bg-slate-700/50 text-slate-300 hover:text-white'
            }`}
            title="Toggle Wireframe"
          >
            <Grid3X3 className="w-5 h-5" />
          </button>
          <button
            onClick={() => setShowGrid(!showGrid)}
            className={`p-2 rounded-lg transition-colors ${
              showGrid 
                ? 'bg-blue-500/20 text-blue-400' 
                : 'hover:bg-slate-700/50 text-slate-300 hover:text-white'
            }`}
            title="Toggle Grid"
          >
            <Eye className="w-5 h-5" />
          </button>
        </div>

        <div className="flex items-center space-x-2 bg-slate-800/80 backdrop-blur-sm rounded-xl p-2 border border-slate-700/50">
          <select
            value={lighting}
            onChange={(e) => setLighting(e.target.value as any)}
            className="bg-slate-700/50 text-white rounded-lg px-3 py-1 text-sm border border-slate-600/50 focus:border-blue-500/50 focus:outline-none"
          >
            <option value="studio">Studio</option>
            <option value="sunset">Sunset</option>
            <option value="city">City</option>
          </select>
          <Sun className="w-4 h-4 text-slate-400" />
        </div>
      </div>

      {/* Model Info Panel */}
      {model && (
        <div className="absolute top-6 right-6 z-10 bg-slate-800/80 backdrop-blur-sm rounded-xl p-4 border border-slate-700/50 w-80">
          <div className="flex items-center space-x-2 mb-3">
            <Info className="w-5 h-5 text-blue-400" />
            <h3 className="font-medium text-white">Model Information</h3>
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-slate-400">Name:</span>
              <span className="text-white">{model.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Type:</span>
              <span className="text-white uppercase">{model.type}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Size:</span>
              <span className="text-white">{(model.size / 1024 / 1024).toFixed(2)} MB</span>
            </div>
            {model.metadata && (
              <>
                <div className="flex justify-between">
                  <span className="text-slate-400">Vertices:</span>
                  <span className="text-white">{model.metadata.vertices?.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Faces:</span>
                  <span className="text-white">{model.metadata.faces?.toLocaleString()}</span>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* 3D Canvas */}
      <div className="h-full bg-gradient-to-b from-slate-900 to-slate-800">
        <Canvas shadows>
          <PerspectiveCamera ref={cameraRef} makeDefault position={[5, 5, 5]} />
          
          {/* Lighting */}
          <ambientLight intensity={0.4} />
          <directionalLight
            position={[10, 10, 5]}
            intensity={1}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
          />
          
          {/* Environment */}
          <Environment preset={lighting} />
          
          {/* Grid */}
          {showGrid && (
            <gridHelper args={[20, 20, '#334155', '#1e293b']} />
          )}
          
          {/* Model */}
          {model ? (
            <Suspense fallback={<LoadingScreen />}>
              <Model url={model.url} />
              <ContactShadows 
                position={[0, -1, 0]} 
                opacity={0.4} 
                scale={10} 
                blur={2} 
                far={4} 
              />
            </Suspense>
          ) : (
            <Html center>
              <div className="text-center p-8 bg-slate-800/90 backdrop-blur-sm rounded-xl border border-slate-700/50">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-teal-500 rounded-xl flex items-center justify-center">
                  <Eye className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">No Model Selected</h3>
                <p className="text-slate-400">Upload a 3D model or select one from the library</p>
              </div>
            </Html>
          )}
          
          {/* Controls */}
          <OrbitControls 
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            autoRotate={false}
            dampingFactor={0.05}
          />
          
          {/* Performance Stats */}
          <Stats />
        </Canvas>
      </div>
    </div>
  );
};